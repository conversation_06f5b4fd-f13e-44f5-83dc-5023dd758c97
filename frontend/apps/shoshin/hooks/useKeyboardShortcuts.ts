"use client";

import { useCallback, useEffect } from "react";
import { useEditorStore } from "../stores/editorStore";

interface KeyboardShortcutOptions {
  disabled?: boolean;
}

export function useKeyboardShortcuts(options: KeyboardShortcutOptions = {}) {
  const { undo, redo, copy, cut, paste, deleteSelected, canUndo, canRedo } =
    useEditorStore();
  const isInputFocused = useCallback((): boolean => {
    const activeElement = document.activeElement;
    if (!activeElement) return false;

    const tagName = activeElement.tagName.toLowerCase();
    const isInput = ["input", "textarea", "select"].includes(tagName);
    const isContentEditable =
      activeElement.getAttribute("contenteditable") === "true";

    return isInput || isContentEditable;
  }, []);

  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      // Don't handle shortcuts if disabled or if an input is focused
      if (options.disabled || isInputFocused()) {
        return;
      }

      const { ctrlKey, metaKey, shiftKey, key } = event;
      const isCtrlOrCmd = ctrlKey || metaKey;

      // Prevent default browser behavior for our shortcuts
      let shouldPreventDefault = false;

      if (isCtrlOrCmd) {
        switch (key.toLowerCase()) {
          case "z":
            if (shiftKey) {
              // Ctrl+Shift+Z for redo
              if (canRedo()) {
                redo();
              }
            } else {
              // Ctrl+Z for undo
              if (canUndo()) {
                undo();
              }
            }
            shouldPreventDefault = true;
            break;

          case "y":
            // Ctrl+Y for redo (alternative to Ctrl+Shift+Z)
            if (canRedo()) {
              redo();
            }
            shouldPreventDefault = true;
            break;

          case "c":
            // Ctrl+C for copy
            copy();
            shouldPreventDefault = true;
            break;

          case "x":
            // Ctrl+X for cut
            cut();
            shouldPreventDefault = true;
            break;

          case "v":
            // Ctrl+V for paste
            paste();
            shouldPreventDefault = true;
            break;
        }
      }

      // Handle Delete/Backspace for deletion
      if (key === "Delete" || key === "Backspace") {
        deleteSelected();
        shouldPreventDefault = true;
      }

      // Handle Shift+L for auto layout
      if (shiftKey && key.toLowerCase() === "l") {
        window.dispatchEvent(new CustomEvent("trigger-auto-layout"));
        shouldPreventDefault = true;
      }

      if (shouldPreventDefault) {
        event.preventDefault();
        event.stopPropagation();
      }
    },
    [
      options.disabled,
      isInputFocused,
      undo,
      redo,
      copy,
      cut,
      paste,
      deleteSelected,
      canUndo,
      canRedo,
    ],
  );

  useEffect(() => {
    document.addEventListener("keydown", handleKeyDown);

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
    };
  }, [handleKeyDown]);

  return {
    isInputFocused,
  };
}
